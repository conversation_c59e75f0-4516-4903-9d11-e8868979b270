// Dashboard functionality
let budgetChart = null;
let trendsChart = null;

document.addEventListener('DOMContentLoaded', function() {
    if (!requireAuth()) return;
    
    loadDashboardData();
    
    // Refresh button
    document.getElementById('refreshData').addEventListener('click', loadDashboardData);
});

async function loadDashboardData() {
    try {
        showLoading(true);
        
        // Load dashboard data and trends in parallel
        const [dashboardResponse, trendsResponse] = await Promise.all([
            apiRequest('/dashboard'),
            apiRequest('/dashboard/trends?months=6')
        ]);
        
        if (dashboardResponse.ok && trendsResponse.ok) {
            const dashboardData = await dashboardResponse.json();
            const trendsData = await trendsResponse.json();
            
            renderSummaryCards(dashboardData.data);
            renderBudgetChart(dashboardData.data);
            renderBudgetStatus(dashboardData.data);
            renderCategoryBreakdown(dashboardData.data);
            renderTrendsChart(trendsData.data);
            await loadRecentTransactions();
        } else {
            showAlert('error', 'Failed to load dashboard data');
        }
    } catch (error) {
        console.error('Dashboard error:', error);
        showAlert('error', 'Error loading dashboard data');
    } finally {
        showLoading(false);
    }
}

function renderSummaryCards(data) {
    const container = document.getElementById('summaryCards');
    
    const cards = [
        {
            title: 'Total Income',
            value: formatCurrency(data.totalIncome),
            icon: 'fas fa-money-bill-wave',
            color: 'success'
        },
        {
            title: 'Total Spent',
            value: formatCurrency(data.totalSpent),
            icon: 'fas fa-shopping-cart',
            color: 'danger'
        },
        {
            title: 'Remaining Budget',
            value: formatCurrency(data.totalRemaining),
            icon: 'fas fa-piggy-bank',
            color: data.totalRemaining >= 0 ? 'info' : 'warning'
        },
        {
            title: 'Budget Status',
            value: data.overallBudgetStatus.charAt(0).toUpperCase() + data.overallBudgetStatus.slice(1),
            icon: 'fas fa-chart-pie',
            color: data.overallBudgetStatus === 'over' ? 'danger' : 'success'
        }
    ];
    
    container.innerHTML = cards.map(card => `
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card bg-${card.color} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">${card.title}</h6>
                            <h4 class="mb-0">${card.value}</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="${card.icon} fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderBudgetChart(data) {
    const ctx = document.getElementById('budgetChart').getContext('2d');
    
    if (budgetChart) {
        budgetChart.destroy();
    }
    
    budgetChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Needs', 'Wants', 'Investments', 'Donations'],
            datasets: [{
                data: [
                    data.summary.needs.allocated,
                    data.summary.wants.allocated,
                    data.summary.investments.allocated,
                    data.summary.donations.allocated
                ],
                backgroundColor: [
                    '#dc3545', // Red for needs
                    '#ffc107', // Yellow for wants
                    '#28a745', // Green for investments
                    '#17a2b8'  // Blue for donations
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.parsed);
                            const percentage = ((context.parsed / data.totalIncome) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

function renderBudgetStatus(data) {
    const container = document.getElementById('budgetStatus');
    
    const statusColor = data.overallBudgetStatus === 'over' ? 'danger' : 
                       data.overallBudgetStatus === 'under' ? 'success' : 'warning';
    
    container.innerHTML = `
        <div class="text-center">
            <div class="badge bg-${statusColor} fs-6 mb-3">
                ${data.overallBudgetStatus.toUpperCase()} BUDGET
            </div>
            <p class="mb-2"><strong>Total Income:</strong> ${formatCurrency(data.totalIncome)}</p>
            <p class="mb-2"><strong>Total Spent:</strong> ${formatCurrency(data.totalSpent)}</p>
            <p class="mb-0"><strong>Remaining:</strong> ${formatCurrency(data.totalRemaining)}</p>
        </div>
    `;
}

function renderCategoryBreakdown(data) {
    const container = document.getElementById('categoryBreakdown');
    
    const categories = [
        { name: 'needs', title: 'Needs', icon: 'fas fa-shopping-basket', color: 'danger' },
        { name: 'wants', title: 'Wants', icon: 'fas fa-gift', color: 'warning' },
        { name: 'investments', title: 'Investments', icon: 'fas fa-chart-line', color: 'success' },
        { name: 'donations', title: 'Donations', icon: 'fas fa-heart', color: 'info' }
    ];
    
    container.innerHTML = categories.map(category => {
        const categoryData = data.summary[category.name];
        const progressPercentage = Math.min((categoryData.spent / categoryData.allocated) * 100, 100);
        const progressColor = categoryData.isOverBudget ? 'danger' : 'success';
        
        return `
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">
                                <i class="${category.icon} me-2"></i>${category.title}
                            </h6>
                            ${categoryData.isOverBudget ? '<span class="badge bg-danger">Over</span>' : ''}
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Allocated: ${formatCurrency(categoryData.allocated)}</small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Spent: ${formatCurrency(categoryData.spent)}</small>
                        </div>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar bg-${progressColor}" 
                                 style="width: ${progressPercentage}%"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">${formatPercentage(categoryData.percentUsed)} used</small>
                            <small class="text-muted">${formatCurrency(categoryData.remaining)} left</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function renderTrendsChart(data) {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    
    if (trendsChart) {
        trendsChart.destroy();
    }
    
    const labels = data.map(item => {
        const date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    
    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Income',
                    data: data.map(item => item.income),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Needs',
                    data: data.map(item => item.needs),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Wants',
                    data: data.map(item => item.wants),
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Investments',
                    data: data.map(item => item.investments),
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

async function loadRecentTransactions() {
    try {
        // Load recent transactions from all categories
        const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];
        const promises = categories.map(category => 
            apiRequest(`/${category}?limit=5&page=1`)
        );
        
        const responses = await Promise.all(promises);
        const allTransactions = [];
        
        for (let i = 0; i < responses.length; i++) {
            if (responses[i].ok) {
                const data = await responses[i].json();
                data.data.forEach(transaction => {
                    allTransactions.push({
                        ...transaction,
                        category: categories[i]
                    });
                });
            }
        }
        
        // Sort by date (newest first) and take top 10
        allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));
        const recentTransactions = allTransactions.slice(0, 10);
        
        renderRecentTransactions(recentTransactions);
    } catch (error) {
        console.error('Error loading recent transactions:', error);
    }
}

function renderRecentTransactions(transactions) {
    const tbody = document.querySelector('#recentTransactions tbody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">No recent transactions</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>${formatDate(transaction.date)}</td>
            <td>
                <span class="badge bg-secondary">${transaction.category}</span>
            </td>
            <td>${transaction.description}</td>
            <td class="text-end">${formatCurrency(transaction.amount)}</td>
        </tr>
    `).join('');
}

function showLoading(show) {
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
        if (show) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        } else {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
        }
    }
}

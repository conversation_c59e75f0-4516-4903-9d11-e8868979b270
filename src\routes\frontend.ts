import { Router } from 'express';
import { FrontendController } from '../controllers/frontendController';

const router = Router();

// Authentication routes
router.get('/login', FrontendController.renderLogin);
router.get('/register', FrontendController.renderRegister);

// Dashboard
router.get('/dashboard', FrontendController.renderDashboard);

// Transaction routes for each category
const categories = ['incomes', 'needs', 'wants', 'investments', 'donations'];

categories.forEach(category => {
  // List page
  router.get(`/${category}`, FrontendController.renderTransactions);
  
  // New transaction form
  router.get(`/${category}/new`, FrontendController.renderTransactionForm);
  
  // Edit transaction form
  router.get(`/${category}/:id/edit`, FrontendController.renderTransactionForm);
});

// Root redirect
router.get('/', (req, res) => {
  res.redirect('/dashboard');
});

// 404 handler
router.get('*', FrontendController.render404);

export default router;
